# Guide de Configuration Détaillé

Ce guide vous accompagne pas à pas dans la configuration complète du workflow WhatsApp n8n.

## 🎯 Vue d'Ensemble

Le workflow utilise l'API [go-whatsapp-multidevice-rest](https://github.com/dimaskiddo/go-whatsapp-multidevice-rest) pour envoyer des messages WhatsApp. Il nécessite :

1. Un serveur API WhatsApp fonctionnel
2. Une authentification WhatsApp active (QR Code scanné)
3. Des credentials n8n configurés
4. Le workflow importé et configuré

## 🚀 Étape 1: Installation de l'API WhatsApp

### Option A: Docker (Recommandé)

```bash
# Démarrer le conteneur
docker run -d \
  -p 3000:3000 \
  --name go-whatsapp-multidevice \
  --restart unless-stopped \
  dimaskiddo/go-whatsapp-multidevice-rest:latest

# Vérifier que le conteneur fonctionne
docker ps
docker logs go-whatsapp-multidevice
```

### Option B: Binaire Pré-compilé

1. Téléchargez la dernière version depuis [GitHub Releases](https://github.com/dimaskiddo/go-whatsapp-multidevice-rest/releases)
2. Extrayez l'archive
3. Copiez `.env.example` vers `.env`
4. Lancez l'exécutable :

```bash
# Linux/macOS
chmod +x go-whatsapp-multidevice-rest
./go-whatsapp-multidevice-rest

# Windows
go-whatsapp-multidevice-rest.exe
```

### Option C: Compilation depuis les Sources

```bash
# Cloner le repository
git clone https://github.com/dimaskiddo/go-whatsapp-multidevice-rest.git
cd go-whatsapp-multidevice-rest

# Installer les dépendances
make vendor

# Configurer l'environnement
cp .env.development .env

# Lancer en mode développement
make run

# Ou compiler
make build
```

## 📱 Étape 2: Authentification WhatsApp

### 2.1 Accéder à l'Interface API

Ouvrez votre navigateur et allez à : `http://localhost:3000/api/v1/whatsapp/docs/`

### 2.2 Générer le QR Code

1. Dans l'interface Swagger, trouvez l'endpoint `/login`
2. Cliquez sur "Try it out"
3. Sélectionnez le format de sortie :
   - `html` : Affichage direct du QR code
   - `json` : Données du QR code en JSON
4. Cliquez sur "Execute"

### 2.3 Scanner le QR Code

1. Ouvrez WhatsApp sur votre téléphone
2. Allez dans **Paramètres > Appareils liés**
3. Appuyez sur **Lier un appareil**
4. Scannez le QR code affiché

### 2.4 Vérifier la Connexion

Une fois connecté, vous devriez voir un message de succès. Testez avec l'endpoint `/registered` pour vérifier qu'un numéro est bien enregistré.

## 🔐 Étape 3: Configuration de l'Authentification

### 3.1 Obtenir le Token d'Authentification

L'API utilise une authentification basique ou Bearer Token. Consultez la documentation de l'API pour obtenir votre token.

### 3.2 Créer les Credentials dans n8n

1. **Accédez aux Credentials** :
   - Dans n8n, allez dans **Settings > Credentials**
   - Cliquez sur **Add Credential**

2. **Choisir le Type** :
   - Sélectionnez **Header Auth**
   - Donnez un nom : `WhatsApp API Auth`

3. **Configurer l'Authentification** :
   ```
   Header Name: Authorization
   Header Value: Bearer YOUR_TOKEN_HERE
   ```
   
   Ou pour l'authentification basique :
   ```
   Header Name: Authorization
   Header Value: Basic BASE64_ENCODED_CREDENTIALS
   ```

4. **Tester la Connexion** :
   - Cliquez sur **Test** si disponible
   - Ou testez manuellement avec un appel API

### 3.3 Alternative: Authentification Basique

Si vous utilisez l'authentification basique :

1. Créez un credential de type **HTTP Basic Auth**
2. Configurez :
   ```
   Username: votre_username
   Password: votre_password
   ```

## 📥 Étape 4: Importation du Workflow

### 4.1 Télécharger le Workflow

Copiez le contenu du fichier `workflows/whatsapp-multitype-sender.json`

### 4.2 Importer dans n8n

1. Dans n8n, cliquez sur le menu **≡** (hamburger)
2. Sélectionnez **Import from Clipboard**
3. Collez le JSON du workflow
4. Cliquez sur **Import**

### 4.3 Vérifier l'Importation

Le workflow devrait apparaître avec le nom "WhatsApp Multi-Type Message Sender - Enhanced"

## ⚙️ Étape 5: Configuration du Workflow

### 5.1 Configurer les Nœuds HTTP Request

Pour chaque nœud HTTP Request dans le workflow :

1. **Ouvrir le nœud** (double-clic)
2. **Configurer l'authentification** :
   - Authentication: `Generic Credential Type`
   - Generic Auth Type: `Header Auth`
   - Credential: Sélectionnez `WhatsApp API Auth`
3. **Vérifier l'URL** :
   - L'URL devrait être : `={{ $json.apiBaseUrl }}/send/[type]`
4. **Sauvegarder** le nœud

### 5.2 Nœuds à Configurer

Configurez l'authentification pour ces nœuds :
- Send Text Message
- Send Image Message
- Send Video Message
- Send Audio Message
- Send Document Message
- Send Sticker Message
- Send Location Message
- Send Contact Message
- Send Link Message
- Send Poll Message

### 5.3 Configuration Optionnelle

Dans le nœud "Process Input Data", vous pouvez modifier :
- L'URL de base par défaut de l'API
- Le format des destinataires par défaut
- Les valeurs par défaut pour chaque type de message

## 🧪 Étape 6: Tests et Validation

### 6.1 Test Simple

1. **Activer le workflow** (bouton ON/OFF)
2. **Cliquer sur "Execute Workflow"**
3. **Entrer des données de test** :
   ```json
   {
     "recipients": "<EMAIL>",
     "messageType": "text",
     "textMessage": "Test depuis n8n!"
   }
   ```
4. **Exécuter et vérifier** les résultats

### 6.2 Test de Validation

Testez la validation avec des données incorrectes :
```json
{
  "recipients": "",
  "messageType": "text",
  "textMessage": ""
}
```

Vous devriez recevoir une erreur de validation.

### 6.3 Test Multi-Destinataires

```json
{
  "recipients": "<EMAIL>,<EMAIL>",
  "messageType": "text",
  "textMessage": "Test multi-destinataires"
}
```

## 🔧 Étape 7: Configuration Avancée

### 7.1 Variables d'Environnement n8n

Créez des variables pour une configuration flexible :

1. **Allez dans Settings > Variables**
2. **Ajoutez ces variables** :
   ```
   WHATSAPP_API_URL = http://localhost:3000/api/v1/whatsapp
   DEFAULT_RECIPIENT = <EMAIL>
   WELCOME_MESSAGE = Bienvenue dans notre service!
   ```

### 7.2 Utilisation des Variables

Dans le workflow, utilisez :
```json
{
  "apiBaseUrl": "{{ $vars.WHATSAPP_API_URL }}",
  "recipients": "{{ $vars.DEFAULT_RECIPIENT }}",
  "textMessage": "{{ $vars.WELCOME_MESSAGE }}"
}
```

### 7.3 Webhook pour Déclenchement Externe

1. **Remplacer le Manual Trigger** par un **Webhook**
2. **Configurer le Webhook** :
   - HTTP Method: POST
   - Path: `/whatsapp-send`
3. **Tester avec curl** :
   ```bash
   curl -X POST http://your-n8n-instance/webhook/whatsapp-send \
     -H "Content-Type: application/json" \
     -d '{
       "recipients": "<EMAIL>",
       "messageType": "text",
       "textMessage": "Message via webhook!"
     }'
   ```

## 🚨 Dépannage

### Problèmes Courants

#### 1. Erreur d'Authentification
```
Error: 401 Unauthorized
```
**Solution** : Vérifiez vos credentials et le token d'authentification

#### 2. Erreur de Connexion
```
Error: ECONNREFUSED
```
**Solution** : Vérifiez que l'API WhatsApp fonctionne sur le bon port

#### 3. QR Code Expiré
```
Error: QR Code expired
```
**Solution** : Régénérez un nouveau QR code et rescannez

#### 4. Format de Numéro Incorrect
```
Error: Invalid phone number format
```
**Solution** : Utilisez le format international avec `@s.whatsapp.net`

### Logs et Débogage

1. **Logs n8n** : Consultez les logs d'exécution dans l'interface
2. **Logs API** : Vérifiez les logs du conteneur Docker
3. **Test Manuel** : Testez l'API directement avec Postman/curl

### Vérifications de Santé

```bash
# Vérifier l'API
curl http://localhost:3000/api/v1/whatsapp/

# Vérifier l'authentification
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/v1/whatsapp/registered?msisdn=6281234567890
```

## 📊 Monitoring et Maintenance

### Surveillance

1. **Monitoring de l'API** : Surveillez la disponibilité du service
2. **Logs d'Exécution** : Consultez régulièrement les logs n8n
3. **Statut WhatsApp** : Vérifiez périodiquement la connexion WhatsApp

### Maintenance

1. **Mise à jour de l'API** : Mettez à jour régulièrement l'image Docker
2. **Sauvegarde** : Sauvegardez vos workflows et credentials
3. **Reconnexion** : Reconnectez WhatsApp si nécessaire

Votre workflow est maintenant prêt à être utilisé ! 🎉
