{"testCases": [{"name": "Test Message Texte Simple", "description": "Test d'envoi d'un message texte basique", "input": {"recipients": "<EMAIL>", "messageType": "text", "textMessage": "<PERSON><PERSON><PERSON>, ceci est un test depuis n8n!"}, "expectedResult": {"status": "success", "messageType": "text", "totalRecipients": 1}}, {"name": "Test Multi-Destinataires", "description": "Test d'envoi à plusieurs destinataires", "input": {"recipients": "<EMAIL>,<EMAIL>,<EMAIL>", "messageType": "text", "textMessage": "Message groupé pour test"}, "expectedResult": {"status": "success", "messageType": "text", "totalRecipients": 3}}, {"name": "Test Image avec Légende", "description": "Test d'envoi d'image avec légende", "input": {"recipients": "<EMAIL>", "messageType": "image", "mediaCaption": "Voici une image de test", "viewOnce": false}, "expectedResult": {"status": "success", "messageType": "image", "totalRecipients": 1}}, {"name": "Test Localisation", "description": "Test d'envoi de coordonnées GPS", "input": {"recipients": "<EMAIL>", "messageType": "location", "latitude": -6.2088, "longitude": 106.8456}, "expectedResult": {"status": "success", "messageType": "location", "totalRecipients": 1}}, {"name": "Test Contact", "description": "Test d'envoi d'informations de contact", "input": {"recipients": "<EMAIL>", "messageType": "contact", "contactName": "<PERSON>", "contactPhone": "6281234567890"}, "expectedResult": {"status": "success", "messageType": "contact", "totalRecipients": 1}}, {"name": "Test Lien", "description": "Test d'envoi de lien avec prévisualisation", "input": {"recipients": "<EMAIL>", "messageType": "link", "linkUrl": "https://www.example.com", "linkCaption": "Visitez notre site web"}, "expectedResult": {"status": "success", "messageType": "link", "totalRecipients": 1}}, {"name": "Test Sondage Simple", "description": "Test de création d'un sondage à choix unique", "input": {"recipients": "<EMAIL>", "messageType": "poll", "pollQuestion": "Quelle est votre couleur préférée?", "pollOptions": "<PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,J<PERSON>ne", "multiAnswer": false}, "expectedResult": {"status": "success", "messageType": "poll", "totalRecipients": 1}}, {"name": "Test Sondage Multi-Réponses", "description": "Test de création d'un sondage à choix multiples", "input": {"recipients": "<EMAIL>", "messageType": "poll", "pollQuestion": "Quels services vous intéressent?", "pollOptions": "Livraison,Installation,Maintenance,Support", "multiAnswer": true}, "expectedResult": {"status": "success", "messageType": "poll", "totalRecipients": 1}}, {"name": "Test Validation - <PERSON><PERSON><PERSON><PERSON>nts", "description": "Test de validation avec destinataires vides", "input": {"recipients": "", "messageType": "text", "textMessage": "Message test"}, "expectedResult": {"status": "error", "message": "Validation failed", "errors": "Recipients field is required"}}, {"name": "Test Validation - Message Texte Manquant", "description": "Test de validation avec message texte vide", "input": {"recipients": "<EMAIL>", "messageType": "text", "textMessage": ""}, "expectedResult": {"status": "error", "message": "Validation failed", "errors": "Text message is required for text type"}}, {"name": "Test Validation - Coordonnées Manquantes", "description": "Test de validation pour localisation sans coordonnées", "input": {"recipients": "<EMAIL>", "messageType": "location", "latitude": "", "longitude": ""}, "expectedResult": {"status": "error", "message": "Validation failed", "errors": "Latitude and longitude are required for location type"}}, {"name": "Test Validation - Contact Incomplet", "description": "Test de validation pour contact sans nom ou téléphone", "input": {"recipients": "<EMAIL>", "messageType": "contact", "contactName": "", "contactPhone": ""}, "expectedResult": {"status": "error", "message": "Validation failed", "errors": "Contact name and phone are required for contact type"}}, {"name": "Test Validation - <PERSON><PERSON>", "description": "Test de validation pour lien sans URL", "input": {"recipients": "<EMAIL>", "messageType": "link", "linkUrl": "", "linkCaption": "Légende sans lien"}, "expectedResult": {"status": "error", "message": "Validation failed", "errors": "Link URL is required for link type"}}, {"name": "Test Validation - Sondage Incomplet", "description": "Test de validation pour sondage sans question ou options", "input": {"recipients": "<EMAIL>", "messageType": "poll", "pollQuestion": "", "pollOptions": ""}, "expectedResult": {"status": "error", "message": "Validation failed", "errors": "Poll question and options are required for poll type"}}, {"name": "Test Format Numéro - Sans Suffixe", "description": "Test de formatage automatique des numéros", "input": {"recipients": "6281234567890", "messageType": "text", "textMessage": "Test formatage numéro"}, "expectedResult": {"status": "success", "messageType": "text", "totalRecipients": 1}}, {"name": "Test API URL Personnalisée", "description": "Test avec URL d'API personnalisée", "input": {"recipients": "<EMAIL>", "messageType": "text", "textMessage": "Test API personnalisée", "apiBaseUrl": "https://mon-api.example.com/api/v1/whatsapp"}, "expectedResult": {"status": "success", "messageType": "text", "totalRecipients": 1}}, {"name": "Test Vidéo Vue Unique", "description": "Test d'envoi de vidéo en vue unique", "input": {"recipients": "<EMAIL>", "messageType": "video", "mediaCaption": "<PERSON><PERSON><PERSON><PERSON>", "viewOnce": true}, "expectedResult": {"status": "success", "messageType": "video", "totalRecipients": 1}}, {"name": "Test Audio", "description": "Test d'envoi de fichier audio", "input": {"recipients": "<EMAIL>", "messageType": "audio"}, "expectedResult": {"status": "success", "messageType": "audio", "totalRecipients": 1}}, {"name": "Test Document", "description": "Test d'envoi de document", "input": {"recipients": "<EMAIL>", "messageType": "document"}, "expectedResult": {"status": "success", "messageType": "document", "totalRecipients": 1}}, {"name": "Test Sticker", "description": "Test d'envoi de sticker", "input": {"recipients": "<EMAIL>", "messageType": "sticker"}, "expectedResult": {"status": "success", "messageType": "sticker", "totalRecipients": 1}}], "testInstructions": {"setup": ["1. Assurez-vous que l'API WhatsApp est démarrée et accessible", "2. Vérifiez que WhatsApp est authentifié (QR code scanné)", "3. <PERSON><PERSON><PERSON><PERSON> les credentials n8n avec le bon token", "4. <PERSON><PERSON><PERSON><PERSON> le workflow dans n8n", "5. <PERSON><PERSON><PERSON><PERSON> les numéros de test par de vrais numéros"], "execution": ["1. <PERSON><PERSON> le workflow dans n8n", "2. <PERSON>ur chaque test case, cliquez sur 'Execute Workflow'", "3. <PERSON><PERSON><PERSON> l'input JSON dans l'interface d'exécution", "4. Exécutez et vérifiez le résultat", "5. Comparez avec le résultat attendu"], "validation": ["1. Vérifiez que les messages sont bien reçus sur WhatsApp", "2. <PERSON><PERSON><PERSON><PERSON><PERSON> les logs d'exécution pour les erreurs", "3. <PERSON><PERSON><PERSON> les réponses JSON retournées", "4. <PERSON>ez les cas d'erreur pour s'assurer qu'ils échouent correctement"]}, "notes": {"phoneNumbers": "Remplacez 6281234567890 par de vrais numéros de test", "mediaFiles": "Pour les tests de médias, vous devrez uploader des fichiers via l'interface", "apiUrl": "Ajustez l'URL de base de l'API selon votre configuration", "authentication": "Assurez-vous que l'authentification est correctement configurée"}}