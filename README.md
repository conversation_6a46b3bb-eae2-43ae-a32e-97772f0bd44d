# Workflow n8n WhatsApp Multi-Type Message Sender

Ce projet fournit un workflow n8n complet pour envoyer différents types de messages WhatsApp via l'API [go-whatsapp-multidevice-rest](https://github.com/dimaskiddo/go-whatsapp-multidevice-rest).

## 🚀 Fonctionnalités

### Types de messages supportés
- ✅ **Messages texte** - Envoi de messages texte simples
- ✅ **Images** - Envoi d'images avec légende et option "vue unique"
- ✅ **Vidéos** - Envoi de vidéos avec légende et option "vue unique"
- ✅ **Audio** - Envoi de fichiers audio
- ✅ **Documents** - Envoi de documents (PDF, DOC, etc.)
- ✅ **Stickers** - Envoi de stickers
- ✅ **Localisation** - Envoi de coordonnées GPS
- ✅ **Contacts** - Envoi d'informations de contact
- ✅ **Liens** - Envoi de liens avec prévisualisation
- ✅ **Sondages** - Création de sondages avec options multiples

### Fonctionnalités avancées
- 🔄 **Multi-destinataires** - Envoi à plusieurs contacts simultanément
- ✅ **Validation des données** - Vérification automatique des paramètres
- 🛡️ **Gestion d'erreurs** - Traitement robuste des erreurs
- 📊 **Réponses détaillées** - Retour d'informations complet sur l'envoi
- 🔐 **Authentification sécurisée** - Support de l'authentification Bearer Token

## 📋 Prérequis

### 1. API WhatsApp
- Serveur go-whatsapp-multidevice-rest en fonctionnement
- URL de base de l'API (par défaut: `http://localhost:3000/api/v1/whatsapp`)
- Token d'authentification configuré

### 2. n8n
- Instance n8n installée et configurée
- Accès à l'éditeur de workflows

## 🛠️ Installation et Configuration

### Étape 1: Démarrer l'API WhatsApp

```bash
# Avec Docker
docker run -d \
  -p 3000:3000 \
  --name go-whatsapp-multidevice \
  --rm dimaskiddo/go-whatsapp-multidevice-rest:latest

# Ou télécharger les binaires depuis GitHub
# https://github.com/dimaskiddo/go-whatsapp-multidevice-rest/releases
```

### Étape 2: Authentification WhatsApp

1. Accédez à `http://localhost:3000/api/v1/whatsapp/docs/`
2. Utilisez l'endpoint `/login` pour générer un QR code
3. Scannez le QR code avec WhatsApp sur votre téléphone

### Étape 3: Configuration des Credentials n8n

1. Dans n8n, allez dans **Settings > Credentials**
2. Créez un nouveau credential de type **Header Auth**
3. Configurez:
   - **Name**: `WhatsApp API Auth`
   - **Header Name**: `Authorization`
   - **Header Value**: `Bearer YOUR_TOKEN_HERE`

### Étape 4: Importer le Workflow

1. Copiez le contenu du fichier `workflows/whatsapp-multitype-sender.json`
2. Dans n8n, cliquez sur **Import from Clipboard**
3. Collez le JSON et importez

### Étape 5: Configuration du Workflow

1. Ouvrez le workflow importé
2. Pour chaque nœud HTTP Request, configurez:
   - **Authentication**: Generic Credential Type > Header Auth
   - **Credential**: Sélectionnez le credential créé à l'étape 3

## 📖 Guide d'Utilisation

### Format des Données d'Entrée

Le workflow accepte un objet JSON avec les paramètres suivants :

```json
{
  "recipients": "<EMAIL>,<EMAIL>",
  "messageType": "text",
  "apiBaseUrl": "http://localhost:3000/api/v1/whatsapp",
  
  // Pour les messages texte
  "textMessage": "Bonjour depuis n8n!",
  
  // Pour les médias (image, video)
  "mediaCaption": "Légende du média",
  "viewOnce": false,
  
  // Pour la localisation
  "latitude": -6.2088,
  "longitude": 106.8456,
  
  // Pour les contacts
  "contactName": "John Doe",
  "contactPhone": "6281234567890",
  
  // Pour les liens
  "linkUrl": "https://example.com",
  "linkCaption": "Visitez notre site",
  
  // Pour les sondages
  "pollQuestion": "Quelle est votre couleur préférée?",
  "pollOptions": "Rouge,Bleu,Vert,Jaune",
  "multiAnswer": false
}
```

### Exemples d'Utilisation

#### 1. Message Texte Simple
```json
{
  "recipients": "<EMAIL>",
  "messageType": "text",
  "textMessage": "Bonjour! Comment allez-vous?"
}
```

#### 2. Image avec Légende
```json
{
  "recipients": "<EMAIL>",
  "messageType": "image",
  "mediaCaption": "Voici une belle photo!",
  "viewOnce": true
}
```

#### 3. Localisation
```json
{
  "recipients": "<EMAIL>",
  "messageType": "location",
  "latitude": -6.2088,
  "longitude": 106.8456
}
```

#### 4. Sondage Multi-Réponses
```json
{
  "recipients": "<EMAIL>",
  "messageType": "poll",
  "pollQuestion": "Quels sont vos hobbies?",
  "pollOptions": "Lecture,Sport,Musique,Voyage,Cuisine",
  "multiAnswer": true
}
```

#### 5. Multi-Destinataires
```json
{
  "recipients": "<EMAIL>,<EMAIL>,<EMAIL>",
  "messageType": "text",
  "textMessage": "Message groupé pour tous!"
}
```

## 🔧 Structure du Workflow

Le workflow est organisé en plusieurs étapes :

1. **Manual Trigger** - Point d'entrée du workflow
2. **Process Input Data** - Traitement et normalisation des données
3. **Input Validator** - Validation des paramètres requis
4. **Validation Check** - Vérification de la validité des données
5. **Message Type Router** - Routage selon le type de message
6. **Send [Type] Message** - Nœuds d'envoi spécialisés par type
7. **Multi-Recipient Processor** - Gestion des destinataires multiples
8. **Check More Recipients** - Vérification des destinataires restants
9. **Final Response** - Réponse finale avec statut

## 🚨 Gestion d'Erreurs

Le workflow inclut une gestion d'erreurs robuste :

- **Validation des données** : Vérification automatique des champs requis
- **Formatage des numéros** : Ajout automatique du suffixe WhatsApp
- **Réponses d'erreur** : Messages d'erreur détaillés en cas de problème
- **Gestion des échecs API** : Traitement des erreurs de l'API WhatsApp

## 📊 Format des Réponses

### Succès
```json
{
  "status": "success",
  "message": "All messages sent successfully",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "messageType": "text",
  "totalRecipients": 2,
  "apiResponse": "{\"status\":\"success\",\"message\":\"Message sent\"}"
}
```

### Erreur de Validation
```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": "Recipients field is required, Text message is required for text type"
}
```

## 🔄 Intégrations Possibles

Ce workflow peut être intégré avec :

- **Webhooks** - Déclenchement via API REST
- **Cron Jobs** - Envoi programmé de messages
- **Bases de données** - Récupération de listes de contacts
- **CRM** - Intégration avec Salesforce, HubSpot, etc.
- **E-commerce** - Notifications de commandes
- **Formulaires** - Notifications de soumission

## 🛡️ Sécurité

- Utilisez HTTPS pour toutes les communications
- Stockez les tokens d'authentification de manière sécurisée
- Limitez l'accès au workflow aux utilisateurs autorisés
- Surveillez les logs pour détecter les utilisations suspectes

## 📝 Notes Importantes

1. **Format des numéros** : Utilisez le format international avec le suffixe `@s.whatsapp.net`
2. **Limites de l'API** : Respectez les limites de débit de l'API WhatsApp
3. **Fichiers multimédias** : Les fichiers doivent être accessibles via URL ou uploadés via form-data
4. **Groupes WhatsApp** : Utilisez le format `@g.us` pour les groupes

## 🤝 Support

Pour obtenir de l'aide :

1. Consultez la documentation de l'API : https://github.com/dimaskiddo/go-whatsapp-multidevice-rest
2. Vérifiez les logs n8n pour les erreurs détaillées
3. Testez d'abord avec un seul destinataire avant l'envoi en masse

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier LICENSE pour plus de détails.
