version: '3.7'

services:
  n8n:
    image: n8nio/n8n:latest
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      - N8N_HOST=localhost
      - N8N_PORT=5678
      - N8N_PROTOCOL=http
      - NODE_ENV=production
      - GENERIC_TIMEZONE=Europe/Paris
      # Tu peux définir un utilisateur admin si tu veux sécuriser
      # - N8N_BASIC_AUTH_ACTIVE=true
      # - N8N_BASIC_AUTH_USER=admin
      # - N8N_BASIC_AUTH_PASSWORD=monMotDePasse
    volumes:
      - ./n8n_data:/home/<USER>/.n8n
    depends_on:
      - whatsapp-api

  whatsapp-api:
    image: dimaskiddo/go-whatsapp-multidevice-rest:latest
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - HTTP_PORT=3000
      - HTTP_BASE_URL=/api/v1/whatsapp
      - HTTP_TIMEOUT=30
      - LOG_LEVEL=info
    volumes:
      - ./whatsapp_data:/app/storages
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/whatsapp/"]
      interval: 30s
      timeout: 10s
      retries: 3
