# Guide de Dépannage - Workflow WhatsApp n8n

Ce guide vous aide à résoudre les problèmes courants rencontrés avec le workflow WhatsApp.

## 🚨 Problèmes d'Authentification

### Erreur 401 - Unauthorized

**Symptômes :**
```
Error: 401 Unauthorized
Response: {"error": "Authentication failed"}
```

**Causes possibles :**
- Token d'authentification incorrect ou expiré
- Credentials n8n mal configurés
- Header d'authentification manquant

**Solutions :**
1. **Vérifier le token :**
   ```bash
   curl -H "Authorization: Bearer YOUR_TOKEN" \
        http://localhost:3000/api/v1/whatsapp/
   ```

2. **Reconfigurer les credentials n8n :**
   - Allez dans Settings > Credentials
   - Éditez le credential "WhatsApp API Auth"
   - Vérifiez le Header Name: `Authorization`
   - Vérifiez le Header Value: `Bearer YOUR_TOKEN`

3. **Tester l'authentification basique :**
   ```bash
   curl -u username:password \
        http://localhost:3000/api/v1/whatsapp/
   ```

### Erreur 403 - Forbidden

**Symptômes :**
```
Error: 403 Forbidden
Response: {"error": "Access denied"}
```

**Solutions :**
- Vérifiez les permissions de votre token
- Contactez l'administrateur de l'API pour les droits d'accès

## 🔌 Problèmes de Connexion

### Erreur ECONNREFUSED

**Symptômes :**
```
Error: connect ECONNREFUSED 127.0.0.1:3000
```

**Causes possibles :**
- L'API WhatsApp n'est pas démarrée
- Port incorrect
- Firewall bloquant la connexion

**Solutions :**
1. **Vérifier que l'API fonctionne :**
   ```bash
   # Vérifier le processus
   docker ps | grep whatsapp
   
   # Vérifier les logs
   docker logs go-whatsapp-multidevice
   
   # Tester la connexion
   curl http://localhost:3000/api/v1/whatsapp/
   ```

2. **Redémarrer l'API :**
   ```bash
   docker restart go-whatsapp-multidevice
   ```

3. **Vérifier la configuration réseau :**
   ```bash
   netstat -tlnp | grep 3000
   ```

### Erreur ETIMEDOUT

**Symptômes :**
```
Error: ETIMEDOUT
```

**Solutions :**
- Augmentez le timeout dans les options du nœud HTTP Request
- Vérifiez la latence réseau
- Optimisez la taille des données envoyées

## 📱 Problèmes WhatsApp

### QR Code Expiré

**Symptômes :**
```
Error: QR Code expired or invalid
```

**Solutions :**
1. **Régénérer le QR Code :**
   - Accédez à `http://localhost:3000/api/v1/whatsapp/docs/`
   - Utilisez l'endpoint `/login`
   - Scannez le nouveau QR code

2. **Vérifier la connexion :**
   ```bash
   curl http://localhost:3000/api/v1/whatsapp/registered?msisdn=6281234567890
   ```

### Session WhatsApp Fermée

**Symptômes :**
```
Error: WhatsApp session not found
```

**Solutions :**
1. **Reconnecter WhatsApp :**
   - Utilisez l'endpoint `/login` pour un nouveau QR code
   - Rescannez avec votre téléphone

2. **Vérifier le statut de la session :**
   - Consultez les logs de l'API
   - Redémarrez l'API si nécessaire

## 📝 Problèmes de Validation

### Erreur de Format de Numéro

**Symptômes :**
```
Error: Invalid phone number format
```

**Solutions :**
1. **Format correct :**
   ```json
   {
     "recipients": "<EMAIL>"
   }
   ```

2. **Format international :**
   - Utilisez le code pays (ex: 62 pour l'Indonésie)
   - Pas de + au début
   - Ajoutez @s.whatsapp.net pour les contacts individuels
   - Utilisez @g.us pour les groupes

### Erreur de Validation des Données

**Symptômes :**
```json
{
  "status": "error",
  "message": "Validation failed",
  "errors": "Text message is required for text type"
}
```

**Solutions :**
1. **Vérifiez les champs requis selon le type :**
   - **text** : `textMessage`
   - **location** : `latitude`, `longitude`
   - **contact** : `contactName`, `contactPhone`
   - **link** : `linkUrl`
   - **poll** : `pollQuestion`, `pollOptions`

2. **Exemple de données complètes :**
   ```json
   {
     "recipients": "<EMAIL>",
     "messageType": "text",
     "textMessage": "Message complet"
   }
   ```

## 🔄 Problèmes de Workflow

### Nœud Non Exécuté

**Symptômes :**
- Certains nœuds restent gris (non exécutés)
- Le workflow s'arrête prématurément

**Solutions :**
1. **Vérifier les connexions :**
   - Assurez-vous que tous les nœuds sont connectés
   - Vérifiez les conditions dans les nœuds Switch

2. **Déboguer étape par étape :**
   - Exécutez le workflow en mode debug
   - Vérifiez les données à chaque étape

### Erreur de Boucle Infinie

**Symptômes :**
- Le workflow ne se termine jamais
- Consommation excessive de ressources

**Solutions :**
1. **Vérifier la logique de boucle :**
   - Contrôlez le nœud "Check More Recipients"
   - Assurez-vous que la condition d'arrêt fonctionne

2. **Limiter les itérations :**
   - Ajoutez un compteur de sécurité
   - Définissez un nombre maximum de destinataires

## 📊 Problèmes de Performance

### Timeout sur Gros Volumes

**Symptômes :**
```
Error: Request timeout
```

**Solutions :**
1. **Optimiser le traitement :**
   - Réduisez le nombre de destinataires par batch
   - Ajoutez des délais entre les envois

2. **Configuration du timeout :**
   ```json
   {
     "options": {
       "timeout": 30000
     }
   }
   ```

### Limitation de Débit

**Symptômes :**
```
Error: Rate limit exceeded
```

**Solutions :**
1. **Ajouter des délais :**
   - Utilisez le nœud "Wait" entre les envois
   - Implémentez un système de queue

2. **Réduire la fréquence :**
   - Espacez les envois dans le temps
   - Utilisez des triggers programmés

## 🛠️ Outils de Diagnostic

### Commandes de Test

```bash
# Test de connectivité API
curl -v http://localhost:3000/api/v1/whatsapp/

# Test d'authentification
curl -H "Authorization: Bearer TOKEN" \
     http://localhost:3000/api/v1/whatsapp/registered?msisdn=6281234567890

# Test d'envoi direct
curl -X POST http://localhost:3000/api/v1/whatsapp/send/text \
     -H "Authorization: Bearer TOKEN" \
     -F "msisdn=<EMAIL>" \
     -F "message=Test direct"
```

### Logs à Consulter

1. **Logs n8n :**
   - Interface web : Executions > Voir les détails
   - Logs serveur : Consultez les logs de votre instance n8n

2. **Logs API WhatsApp :**
   ```bash
   docker logs go-whatsapp-multidevice -f
   ```

3. **Logs système :**
   ```bash
   # Vérifier les ressources
   top
   df -h
   netstat -tlnp
   ```

## 🔍 Checklist de Dépannage

### Avant de Commencer
- [ ] L'API WhatsApp est-elle démarrée ?
- [ ] WhatsApp est-il authentifié (QR code scanné) ?
- [ ] Les credentials n8n sont-ils configurés ?
- [ ] Le workflow est-il importé et activé ?

### Tests de Base
- [ ] Test de connectivité à l'API
- [ ] Test d'authentification
- [ ] Test avec un seul destinataire
- [ ] Test avec des données minimales

### Tests Avancés
- [ ] Test multi-destinataires
- [ ] Test de tous les types de messages
- [ ] Test de gestion d'erreurs
- [ ] Test de performance

## 📞 Support et Ressources

### Documentation Officielle
- [API WhatsApp](https://github.com/dimaskiddo/go-whatsapp-multidevice-rest)
- [Documentation n8n](https://docs.n8n.io/)

### Communauté
- [Forum n8n](https://community.n8n.io/)
- [GitHub Issues](https://github.com/dimaskiddo/go-whatsapp-multidevice-rest/issues)

### Logs Détaillés

Pour activer les logs détaillés :

1. **n8n :**
   ```bash
   export N8N_LOG_LEVEL=debug
   ```

2. **API WhatsApp :**
   - Modifiez le fichier `.env`
   - Ajoutez `LOG_LEVEL=debug`

Avec ces informations, vous devriez pouvoir résoudre la plupart des problèmes rencontrés ! 🚀
