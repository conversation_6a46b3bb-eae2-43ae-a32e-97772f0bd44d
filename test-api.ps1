# Script PowerShell pour tester l'API WhatsApp
param(
    [string]$ApiUrl = "http://localhost:3001/api/v1/whatsapp",
    [string]$Token = "",
    [string]$TestNumber = "<EMAIL>"
)

Write-Host "🚀 Test de l'API WhatsApp" -ForegroundColor Green
Write-Host "================================" -ForegroundColor Green

# Test 1: Vérifier que l'API fonctionne
Write-Host "`n1. Test de connectivité..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$ApiUrl/" -UseBasicParsing
    $content = $response.Content | ConvertFrom-Json
    if ($content.status -eq $true) {
        Write-Host "✅ API accessible - Status: $($content.message)" -ForegroundColor Green
    } else {
        Write-Host "❌ API non accessible" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Erreur de connexion: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Vérifier l'authentification (si token fourni)
if ($Token -ne "") {
    Write-Host "`n2. Test d'authentification..." -ForegroundColor Yellow
    try {
        $headers = @{
            "Authorization" = "Bearer $Token"
        }
        $response = Invoke-WebRequest -Uri "$ApiUrl/registered?msisdn=$TestNumber" -Headers $headers -UseBasicParsing
        Write-Host "✅ Authentification réussie" -ForegroundColor Green
    } catch {
        Write-Host "❌ Erreur d'authentification: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "💡 Vérifiez votre token d'authentification" -ForegroundColor Cyan
    }
} else {
    Write-Host "`n2. Test d'authentification ignoré (pas de token fourni)" -ForegroundColor Gray
}

# Test 3: Vérifier les endpoints disponibles
Write-Host "`n3. Endpoints disponibles:" -ForegroundColor Yellow
$endpoints = @(
    "/login - Authentification WhatsApp",
    "/logout - Déconnexion",
    "/send/text - Envoi de texte",
    "/send/image - Envoi d'image",
    "/send/video - Envoi de vidéo",
    "/send/audio - Envoi d'audio",
    "/send/document - Envoi de document",
    "/send/sticker - Envoi de sticker",
    "/send/location - Envoi de localisation",
    "/send/contact - Envoi de contact",
    "/send/link - Envoi de lien",
    "/send/poll - Envoi de sondage"
)

foreach ($endpoint in $endpoints) {
    Write-Host "  📍 $endpoint" -ForegroundColor Cyan
}

Write-Host "`n🔗 Liens utiles:" -ForegroundColor Yellow
Write-Host "  📚 Documentation API: $ApiUrl/docs/" -ForegroundColor Cyan
Write-Host "  🔧 Interface n8n: http://localhost:5678" -ForegroundColor Cyan

Write-Host "`n📋 Prochaines étapes:" -ForegroundColor Yellow
Write-Host "  1. Authentifiez WhatsApp via: $ApiUrl/docs/" -ForegroundColor White
Write-Host "  2. Configurez les credentials dans n8n" -ForegroundColor White
Write-Host "  3. Importez le workflow" -ForegroundColor White
Write-Host "  4. Testez l'envoi de messages" -ForegroundColor White

Write-Host "`n✨ Test terminé!" -ForegroundColor Green
