{"whatsapp_api": {"base_url": "http://localhost:3001/api/v1/whatsapp", "swagger_docs": "http://localhost:3001/api/v1/whatsapp/docs/", "authentication": {"type": "header", "header_name": "Authorization", "header_value": "Bearer YOUR_TOKEN_HERE", "note": "Remplacez YOUR_TOKEN_HERE par votre token d'authentification"}}, "n8n": {"url": "http://localhost:5678", "workflow_file": "./workflows/whatsapp-multitype-sender.json"}, "test_numbers": {"primary": "<EMAIL>", "secondary": "<EMAIL>", "note": "Remplacez par de vrais numéros de test"}, "setup_steps": ["1. Vérifiez que l'API WhatsApp fonctionne sur http://localhost:3001", "2. Authentifiez WhatsApp via http://localhost:3001/api/v1/whatsapp/docs/", "3. <PERSON>figu<PERSON> les credentials dans n8n (Settings > Credentials)", "4. Importez le workflow depuis workflows/whatsapp-multitype-sender.json", "5. Configurez l'authentification dans chaque nœud HTTP Request", "6. <PERSON><PERSON> avec un message simple"]}