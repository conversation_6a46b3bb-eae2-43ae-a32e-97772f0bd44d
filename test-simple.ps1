# Test simple de l'API WhatsApp
Write-Host "Test de l'API WhatsApp" -ForegroundColor Green

# Test de connectivite
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/v1/whatsapp/" -UseBasicParsing
    $content = $response.Content | ConvertFrom-Json
    Write-Host "API Status: $($content.message)" -ForegroundColor Green
    Write-Host "Documentation: http://localhost:3001/api/v1/whatsapp/docs/" -ForegroundColor Cyan
    Write-Host "Interface n8n: http://localhost:5678" -ForegroundColor Cyan
} catch {
    Write-Host "Erreur: $($_.Exception.Message)" -ForegroundColor Red
}
