# Exemples d'Utilisation du Workflow WhatsApp

Ce document contient des exemples pratiques d'utilisation du workflow n8n WhatsApp Multi-Type Message Sender.

## 📋 Table des Matières

1. [Messages Texte](#messages-texte)
2. [Messages Multimédias](#messages-multimédias)
3. [Localisation et Contacts](#localisation-et-contacts)
4. [Liens et Sondages](#liens-et-sondages)
5. [Scénarios Multi-Destinataires](#scénarios-multi-destinataires)
6. [Intégrations Avancées](#intégrations-avancées)

## 📝 Messages Texte

### Message Simple
```json
{
  "recipients": "<EMAIL>",
  "messageType": "text",
  "textMessage": "Bonjour! J'espère que vous allez bien."
}
```

### Message de Bienvenue
```json
{
  "recipients": "<EMAIL>",
  "messageType": "text",
  "textMessage": "🎉 Bienvenue dans notre service! Nous sommes ravis de vous compter parmi nos clients. N'hésitez pas à nous contacter si vous avez des questions."
}
```

### Notification de Commande
```json
{
  "recipients": "<EMAIL>",
  "messageType": "text",
  "textMessage": "📦 Votre commande #12345 a été expédiée! Numéro de suivi: ABC123456789. Livraison prévue: 2-3 jours ouvrables."
}
```

## 🖼️ Messages Multimédias

### Image avec Légende
```json
{
  "recipients": "<EMAIL>",
  "messageType": "image",
  "mediaCaption": "🌟 Découvrez notre nouveau produit! Disponible dès maintenant en magasin.",
  "viewOnce": false
}
```

### Image Vue Unique (Confidentielle)
```json
{
  "recipients": "<EMAIL>",
  "messageType": "image",
  "mediaCaption": "📋 Voici votre facture confidentielle",
  "viewOnce": true
}
```

### Vidéo Promotionnelle
```json
{
  "recipients": "<EMAIL>",
  "messageType": "video",
  "mediaCaption": "🎬 Regardez notre dernière vidéo promotionnelle! Ne manquez pas nos offres spéciales.",
  "viewOnce": false
}
```

### Document Important
```json
{
  "recipients": "<EMAIL>",
  "messageType": "document"
}
```

### Message Audio
```json
{
  "recipients": "<EMAIL>",
  "messageType": "audio"
}
```

## 📍 Localisation et Contacts

### Partage de Localisation - Bureau
```json
{
  "recipients": "<EMAIL>",
  "messageType": "location",
  "latitude": -6.2088,
  "longitude": 106.8456
}
```

### Partage de Localisation - Événement
```json
{
  "recipients": "<EMAIL>",
  "messageType": "location",
  "latitude": 48.8566,
  "longitude": 2.3522
}
```

### Contact Commercial
```json
{
  "recipients": "<EMAIL>",
  "messageType": "contact",
  "contactName": "Jean Dupont - Commercial",
  "contactPhone": "6281234567890"
}
```

### Contact Support Technique
```json
{
  "recipients": "<EMAIL>",
  "messageType": "contact",
  "contactName": "Support Technique",
  "contactPhone": "6289876543210"
}
```

## 🔗 Liens et Sondages

### Lien vers Site Web
```json
{
  "recipients": "<EMAIL>",
  "messageType": "link",
  "linkUrl": "https://www.monsite.com",
  "linkCaption": "🌐 Visitez notre nouveau site web pour découvrir toutes nos offres!"
}
```

### Lien vers Promotion
```json
{
  "recipients": "<EMAIL>",
  "messageType": "link",
  "linkUrl": "https://www.monsite.com/promo",
  "linkCaption": "🎁 Offre spéciale: -50% sur tous nos produits! Valable jusqu'au 31 décembre."
}
```

### Sondage Simple
```json
{
  "recipients": "<EMAIL>",
  "messageType": "poll",
  "pollQuestion": "Quelle est votre couleur préférée?",
  "pollOptions": "Rouge,Bleu,Vert,Jaune,Violet",
  "multiAnswer": false
}
```

### Sondage Satisfaction Client
```json
{
  "recipients": "<EMAIL>",
  "messageType": "poll",
  "pollQuestion": "Comment évaluez-vous notre service?",
  "pollOptions": "Excellent,Très bien,Bien,Moyen,À améliorer",
  "multiAnswer": false
}
```

### Sondage Multi-Réponses
```json
{
  "recipients": "<EMAIL>",
  "messageType": "poll",
  "pollQuestion": "Quels services vous intéressent? (Plusieurs réponses possibles)",
  "pollOptions": "Livraison,Installation,Maintenance,Formation,Support",
  "multiAnswer": true
}
```

## 👥 Scénarios Multi-Destinataires

### Newsletter Équipe
```json
{
  "recipients": "<EMAIL>,<EMAIL>,<EMAIL>",
  "messageType": "text",
  "textMessage": "📢 Réunion d'équipe demain à 14h en salle de conférence. Ordre du jour en pièce jointe."
}
```

### Alerte Urgente
```json
{
  "recipients": "<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>",
  "messageType": "text",
  "textMessage": "🚨 URGENT: Maintenance système prévue ce soir de 22h à 2h. Services temporairement indisponibles."
}
```

### Promotion Clients VIP
```json
{
  "recipients": "<EMAIL>,<EMAIL>,<EMAIL>",
  "messageType": "image",
  "mediaCaption": "🌟 Offre exclusive VIP: Accès anticipé à notre nouvelle collection avec 30% de réduction!"
}
```

### Sondage Groupe
```json
{
  "recipients": "<EMAIL>,<EMAIL>,<EMAIL>",
  "messageType": "poll",
  "pollQuestion": "Quel jour préférez-vous pour la formation?",
  "pollOptions": "Lundi,Mardi,Mercredi,Jeudi,Vendredi",
  "multiAnswer": false
}
```

## 🔧 Intégrations Avancées

### Avec Webhook (Déclenchement Externe)
```json
{
  "recipients": "{{ $json.customer_phone }}@s.whatsapp.net",
  "messageType": "text",
  "textMessage": "Bonjour {{ $json.customer_name }}, votre commande {{ $json.order_id }} est confirmée!"
}
```

### Avec Base de Données
```json
{
  "recipients": "{{ $json.phone_number }}@s.whatsapp.net",
  "messageType": "text",
  "textMessage": "Rappel: Votre rendez-vous avec {{ $json.doctor_name }} est prévu le {{ $json.appointment_date }} à {{ $json.appointment_time }}."
}
```

### Notification E-commerce
```json
{
  "recipients": "{{ $json.buyer_phone }}@s.whatsapp.net",
  "messageType": "text",
  "textMessage": "🛒 Panier abandonné: {{ $json.product_name }} vous attend! Finalisez votre achat avec le code RETOUR10 pour 10% de réduction."
}
```

### Suivi de Livraison
```json
{
  "recipients": "{{ $json.customer_phone }}@s.whatsapp.net",
  "messageType": "location",
  "latitude": "{{ $json.delivery_lat }}",
  "longitude": "{{ $json.delivery_lng }}"
}
```

## 🎯 Cas d'Usage Métier

### Service Client
```json
{
  "recipients": "<EMAIL>",
  "messageType": "text",
  "textMessage": "Bonjour, nous avons bien reçu votre demande de support (Ticket #{{ $json.ticket_id }}). Un technicien vous contactera dans les 2 heures."
}
```

### Marketing Automation
```json
{
  "recipients": "{{ $json.lead_phone }}@s.whatsapp.net",
  "messageType": "link",
  "linkUrl": "{{ $json.landing_page_url }}",
  "linkCaption": "🎯 Offre personnalisée pour vous! Cliquez pour découvrir nos recommandations basées sur vos préférences."
}
```

### Ressources Humaines
```json
{
  "recipients": "{{ $json.employee_phone }}@s.whatsapp.net",
  "messageType": "document",
  "textMessage": "📄 Voici votre bulletin de paie pour le mois de {{ $json.month }}. Document confidentiel."
}
```

### Événementiel
```json
{
  "recipients": "{{ $json.attendee_phone }}@s.whatsapp.net",
  "messageType": "poll",
  "pollQuestion": "Confirmez-vous votre présence à l'événement {{ $json.event_name }} le {{ $json.event_date }}?",
  "pollOptions": "Oui, je serai présent,Non, je ne peux pas venir,Peut-être",
  "multiAnswer": false
}
```

## ⚙️ Configuration Avancée

### URL API Personnalisée
```json
{
  "recipients": "<EMAIL>",
  "messageType": "text",
  "textMessage": "Message via serveur personnalisé",
  "apiBaseUrl": "https://mon-serveur-whatsapp.com/api/v1/whatsapp"
}
```

### Avec Variables d'Environnement
```json
{
  "recipients": "{{ $vars.default_recipient }}",
  "messageType": "text",
  "textMessage": "{{ $vars.welcome_message }}",
  "apiBaseUrl": "{{ $vars.whatsapp_api_url }}"
}
```

## 🔍 Débogage et Tests

### Test de Validation
```json
{
  "recipients": "",
  "messageType": "text",
  "textMessage": ""
}
```
*Résultat attendu: Erreur de validation*

### Test Multi-Types
```json
{
  "recipients": "<EMAIL>",
  "messageType": "invalid_type",
  "textMessage": "Test"
}
```
*Résultat attendu: Aucun nœud d'envoi déclenché*

Ces exemples couvrent la plupart des cas d'usage courants. Adaptez-les selon vos besoins spécifiques!
