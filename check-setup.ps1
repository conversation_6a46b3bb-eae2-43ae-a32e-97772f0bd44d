# Script de verification de l'installation
Write-Host "🔍 Verification de l'installation WhatsApp + n8n" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Verification des services Docker
Write-Host "`n1. Verification des services Docker..." -ForegroundColor Yellow
try {
    $containers = docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
    Write-Host $containers -ForegroundColor White
} catch {
    Write-Host "❌ Docker non accessible" -ForegroundColor Red
}

# Test API WhatsApp
Write-Host "`n2. Test de l'API WhatsApp..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/v1/whatsapp/" -UseBasicParsing
    $content = $response.Content | ConvertFrom-Json
    Write-Host "✅ API WhatsApp: $($content.message)" -ForegroundColor Green
} catch {
    Write-Host "❌ API WhatsApp non accessible" -ForegroundColor Red
}

# Test n8n
Write-Host "`n3. Test de n8n..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5678" -UseBasicParsing
    Write-Host "✅ n8n accessible" -ForegroundColor Green
} catch {
    Write-Host "❌ n8n non accessible" -ForegroundColor Red
}

# Verification des fichiers
Write-Host "`n4. Verification des fichiers..." -ForegroundColor Yellow
$files = @(
    "workflows/whatsapp-multitype-sender.json",
    "README.md",
    "setup-guide.md",
    "examples.md",
    "NEXT-STEPS.md"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "✅ $file" -ForegroundColor Green
    } else {
        Write-Host "❌ $file manquant" -ForegroundColor Red
    }
}

Write-Host "`n🎯 Prochaines etapes:" -ForegroundColor Cyan
Write-Host "1. Authentifiez WhatsApp: http://localhost:3001/api/v1/whatsapp/docs/" -ForegroundColor White
Write-Host "2. Configurez n8n: http://localhost:5678" -ForegroundColor White
Write-Host "3. Consultez: NEXT-STEPS.md" -ForegroundColor White

Write-Host "`n✨ Verification terminee!" -ForegroundColor Green
