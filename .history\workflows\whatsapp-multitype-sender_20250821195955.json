{"name": "WhatsApp Multi-Type Message Sender - Enhanced", "nodes": [{"parameters": {"options": {}}, "id": "start-trigger", "name": "Manual Trigger", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "recipients", "value": "={{ $json.recipients || '<EMAIL>' }}"}, {"name": "messageType", "value": "={{ $json.messageType || 'text' }}"}, {"name": "textMessage", "value": "={{ $json.textMessage || 'Hello from n8n!' }}"}, {"name": "mediaCaption", "value": "={{ $json.mediaCaption || '' }}"}, {"name": "latitude", "value": "={{ $json.latitude || '' }}"}, {"name": "longitude", "value": "={{ $json.longitude || '' }}"}, {"name": "contactName", "value": "={{ $json.contactName || '' }}"}, {"name": "contactPhone", "value": "={{ $json.contactPhone || '' }}"}, {"name": "linkUrl", "value": "={{ $json.linkUrl || '' }}"}, {"name": "linkCaption", "value": "={{ $json.linkCaption || '' }}"}, {"name": "pollQuestion", "value": "={{ $json.pollQuestion || '' }}"}, {"name": "pollOptions", "value": "={{ $json.pollOptions || '' }}"}, {"name": "multiAnswer", "value": "={{ $json.multiAnswer || false }}"}, {"name": "viewOnce", "value": "={{ $json.viewOnce || false }}"}, {"name": "apiBaseUrl", "value": "={{ $json.apiBaseUrl || 'http://localhost:3001/api/v1/whatsapp' }}"}]}, "options": {}}, "id": "input-processor", "name": "Process Input Data", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [460, 300]}, {"parameters": {"jsCode": "// Validation des données d'entrée\nconst data = $input.first().json;\nconst errors = [];\n\n// Validation des destinataires\nif (!data.recipients || data.recipients.trim() === '') {\n  errors.push('Recipients field is required');\n}\n\n// Validation selon le type de message\nswitch (data.messageType) {\n  case 'text':\n    if (!data.textMessage || data.textMessage.trim() === '') {\n      errors.push('Text message is required for text type');\n    }\n    break;\n  case 'location':\n    if (!data.latitude || !data.longitude) {\n      errors.push('Latitude and longitude are required for location type');\n    }\n    break;\n  case 'contact':\n    if (!data.contactName || !data.contactPhone) {\n      errors.push('Contact name and phone are required for contact type');\n    }\n    break;\n  case 'link':\n    if (!data.linkUrl) {\n      errors.push('Link URL is required for link type');\n    }\n    break;\n  case 'poll':\n    if (!data.pollQuestion || !data.pollOptions) {\n      errors.push('Poll question and options are required for poll type');\n    }\n    break;\n}\n\n// Formatage des destinataires\nlet recipients = data.recipients;\nif (typeof recipients === 'string') {\n  recipients = recipients.split(',').map(r => {\n    const clean = r.trim();\n    // Ajouter @s.whatsapp.net si pas déjà présent\n    return clean.includes('@') ? clean : clean + '@s.whatsapp.net';\n  });\n}\n\nconst result = {\n  ...data,\n  recipients: recipients,\n  errors: errors,\n  isValid: errors.length === 0,\n  timestamp: new Date().toISOString()\n};\n\nreturn [{ json: result }];"}, "id": "input-validator", "name": "Input Validator", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [680, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "validation-failed", "leftValue": "={{ $json.isValid }}", "rightValue": false, "operator": {"type": "boolean", "operation": "equals"}}]}, "options": {}}, "id": "validation-check", "name": "Validation Check", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [900, 300]}, {"parameters": {"values": {"string": [{"name": "status", "value": "error"}, {"name": "message", "value": "Validation failed"}, {"name": "errors", "value": "={{ $json.errors.join(', ') }}"}]}, "options": {}}, "id": "validation-error", "name": "Validation Error", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1120, 180]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "text-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "text", "operator": {"type": "string", "operation": "equals"}}, {"id": "image-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "image", "operator": {"type": "string", "operation": "equals"}}, {"id": "video-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "video", "operator": {"type": "string", "operation": "equals"}}, {"id": "audio-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "audio", "operator": {"type": "string", "operation": "equals"}}, {"id": "document-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "document", "operator": {"type": "string", "operation": "equals"}}, {"id": "sticker-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "sticker", "operator": {"type": "string", "operation": "equals"}}, {"id": "location-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "location", "operator": {"type": "string", "operation": "equals"}}, {"id": "contact-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "contact", "operator": {"type": "string", "operation": "equals"}}, {"id": "link-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "link", "operator": {"type": "string", "operation": "equals"}}, {"id": "poll-condition", "leftValue": "={{ $json.messageType }}", "rightValue": "poll", "operator": {"type": "string", "operation": "equals"}}]}, "options": {}}, "id": "message-type-switch", "name": "Message Type Router", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1120, 420]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/text", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "message", "value": "={{ $json.textMessage }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-text-message", "name": "Send Text Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 200]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/image", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "caption", "value": "={{ $json.mediaCaption }}"}, {"name": "viewonce", "value": "={{ $json.viewOnce }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-image-message", "name": "Send Image Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 280]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/video", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "caption", "value": "={{ $json.mediaCaption }}"}, {"name": "viewonce", "value": "={{ $json.viewOnce }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-video-message", "name": "Send Video Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 360]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/audio", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-audio-message", "name": "Send Audio Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 440]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/document", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-document-message", "name": "Send Document Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 520]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/sticker", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-sticker-message", "name": "Send Sticker Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 600]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/location", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "latitude", "value": "={{ $json.latitude }}"}, {"name": "longitude", "value": "={{ $json.longitude }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-location-message", "name": "Send Location Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 680]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/contact", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "name", "value": "={{ $json.contactName }}"}, {"name": "phone", "value": "={{ $json.contactPhone }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-contact-message", "name": "Send Contact Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 760]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/link", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "url", "value": "={{ $json.linkUrl }}"}, {"name": "caption", "value": "={{ $json.linkCaption }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-link-message", "name": "Send Link Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 840]}, {"parameters": {"method": "POST", "url": "={{ $json.apiBaseUrl }}/send/poll", "authentication": "genericCredentialType", "genericAuthType": "httpHeaderAuth", "sendBody": true, "bodyContentType": "form", "bodyParameters": {"parameters": [{"name": "msisdn", "value": "={{ $json.recipients[0] }}"}, {"name": "question", "value": "={{ $json.pollQuestion }}"}, {"name": "options", "value": "={{ $json.pollOptions }}"}, {"name": "multianswer", "value": "={{ $json.multiAnswer }}"}]}, "options": {"response": {"response": {"neverError": true, "responseFormat": "json"}}}}, "id": "send-poll-message", "name": "Send Poll Message", "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1340, 920]}, {"parameters": {"jsCode": "// Traitement des destinataires multiples et gestion des réponses\nconst inputData = $input.first().json;\nconst apiResponse = $input.first().json;\n\n// Vérifier si c'est une réponse d'API ou des données d'entrée\nlet recipients = [];\nif (Array.isArray(inputData.recipients)) {\n  recipients = inputData.recipients;\n} else if (typeof inputData.recipients === 'string') {\n  recipients = inputData.recipients.split(',').map(r => r.trim());\n}\n\nconst results = [];\n\n// Si on a plusieurs destinataires, traiter chacun\nif (recipients.length > 1) {\n  for (let i = 1; i < recipients.length; i++) {\n    const result = {\n      ...inputData,\n      recipients: [recipients[i]],\n      currentRecipient: recipients[i],\n      recipientIndex: i,\n      totalRecipients: recipients.length,\n      needsProcessing: true\n    };\n    results.push(result);\n  }\n} else {\n  // Un seul destinataire ou traitement terminé\n  const result = {\n    ...inputData,\n    apiResponse: apiResponse,\n    status: apiResponse.status || 'completed',\n    timestamp: new Date().toISOString(),\n    needsProcessing: false\n  };\n  results.push(result);\n}\n\nreturn results.map(result => ({ json: result }));"}, "id": "multi-recipient-processor", "name": "Multi-Recipient Processor", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1560, 560]}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict"}, "conditions": [{"id": "needs-processing", "leftValue": "={{ $json.needsProcessing }}", "rightValue": true, "operator": {"type": "boolean", "operation": "equals"}}]}, "options": {}}, "id": "check-more-recipients", "name": "Check More Recipients", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [1780, 560]}, {"parameters": {"values": {"string": [{"name": "status", "value": "success"}, {"name": "message", "value": "All messages sent successfully"}, {"name": "timestamp", "value": "={{ new Date().toISOString() }}"}, {"name": "messageType", "value": "={{ $json.messageType }}"}, {"name": "totalRecipients", "value": "={{ $json.recipients ? $json.recipients.length : 1 }}"}, {"name": "apiResponse", "value": "={{ JSON.stringify($json.apiResponse) }}"}]}, "options": {}}, "id": "final-response", "name": "Final Response", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [1780, 720]}], "connections": {"Manual Trigger": {"main": [[{"node": "Process Input Data", "type": "main", "index": 0}]]}, "Process Input Data": {"main": [[{"node": "Input Validator", "type": "main", "index": 0}]]}, "Input Validator": {"main": [[{"node": "Validation Check", "type": "main", "index": 0}]]}, "Validation Check": {"main": [[{"node": "Validation Error", "type": "main", "index": 0}], [{"node": "Message Type Router", "type": "main", "index": 0}]]}, "Message Type Router": {"main": [[{"node": "Send Text Message", "type": "main", "index": 0}], [{"node": "Send Image Message", "type": "main", "index": 0}], [{"node": "Send Video Message", "type": "main", "index": 0}], [{"node": "Send Audio Message", "type": "main", "index": 0}], [{"node": "Send Document Message", "type": "main", "index": 0}], [{"node": "Send Sticker Message", "type": "main", "index": 0}], [{"node": "Send Location Message", "type": "main", "index": 0}], [{"node": "Send Contact Message", "type": "main", "index": 0}], [{"node": "Send Link Message", "type": "main", "index": 0}], [{"node": "Send Poll Message", "type": "main", "index": 0}]]}, "Send Text Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Image Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Video Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Audio Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Document Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Sticker Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Location Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Contact Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Link Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Send Poll Message": {"main": [[{"node": "Multi-Recipient Processor", "type": "main", "index": 0}]]}, "Multi-Recipient Processor": {"main": [[{"node": "Check More Recipients", "type": "main", "index": 0}]]}, "Check More Recipients": {"main": [[{"node": "Message Type Router", "type": "main", "index": 0}], [{"node": "Final Response", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "2", "meta": {"templateCredsSetupCompleted": false, "instanceId": "n8n-whatsapp-workflow-enhanced"}, "id": "whatsapp-multitype-sender-enhanced", "tags": [{"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "whatsapp", "name": "WhatsApp"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "messaging", "name": "Messaging"}, {"createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z", "id": "enhanced", "name": "Enhanced"}]}